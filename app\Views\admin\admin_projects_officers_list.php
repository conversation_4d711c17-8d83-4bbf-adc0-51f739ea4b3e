<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary">
    ➕ Assign Officer
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Officers
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage officer assignments and roles for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                📁
            </div>
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <?= esc($project['title']) ?>
                </h3>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Officers Summary -->
<div class="card mb-xl">
    <div class="card-header">
        📊 Officers Summary
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $stats['total_officers'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Officers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_role']['lead'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Lead Officers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_role']['certifier'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Certifiers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_role']['support'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Support Officers</div>
            </div>
        </div>
    </div>
</div>

<!-- Officers List -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>👥</span>
                <span>Assigned Officers</span>
            </div>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary" style="margin-left: auto;">
                ➕ Assign New Officer
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($officers)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php foreach ($officers as $officer): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); border-left: 4px solid <?= $officer['role'] === 'lead' ? 'var(--brand-success)' : ($officer['role'] === 'certifier' ? 'var(--brand-secondary)' : 'var(--brand-accent)') ?>;">
                        <div style="display: flex; align-items: start; justify-content: between; gap: var(--spacing-md);">
                            <!-- Officer Info -->
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                                    <h4 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                                        <?= esc($officer['first_name'] . ' ' . $officer['last_name']) ?>
                                    </h4>
                                    <span style="background: <?= $officer['role'] === 'lead' ? 'var(--brand-success)' : ($officer['role'] === 'certifier' ? 'var(--brand-secondary)' : 'var(--brand-accent)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                        <?php if ($officer['role'] === 'lead'): ?>
                                            👑 Lead Officer
                                        <?php elseif ($officer['role'] === 'certifier'): ?>
                                            ✅ Certifier
                                        <?php else: ?>
                                            🤝 Support
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                                    <span style="color: var(--text-muted); font-weight: 500;">Username:</span>
                                    <span style="color: var(--text-secondary); font-family: monospace;"><?= esc($officer['username']) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Email:</span>
                                    <span style="color: var(--text-secondary);"><?= esc($officer['email']) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Assigned:</span>
                                    <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($officer['created_at'])) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Status:</span>
                                    <span style="color: var(--brand-success);">Active</span>
                                </div>
                            </div>

                            <!-- Officer Actions -->
                            <div style="display: flex; gap: var(--spacing-sm);">
                                <button onclick="showRemoveOfficerModal(<?= $officer['id'] ?>, '<?= esc($officer['first_name'] . ' ' . $officer['last_name']) ?>', '<?= esc($officer['role']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    🗑️ Remove
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👥</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Officers Assigned</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start building your project team by assigning the first officer.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary">
                    ➕ Assign First Officer
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Remove Officer Modal -->
<div id="removeOfficerModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Remove Project Officer</h3>
        
        <form id="removeOfficerForm" method="POST">
            <div style="margin-bottom: var(--spacing-lg);">
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-md);">
                    Are you sure you want to remove <strong id="officerName"></strong> from this project?
                </p>
                <p style="color: var(--text-muted); font-size: 0.875rem;">
                    Role: <span id="officerRole" style="font-weight: 600;"></span>
                </p>
            </div>

            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Removal Reason
                </label>
                <textarea name="removal_reason" rows="3" style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--border-color); border-radius: var(--radius-md); font-family: inherit; resize: vertical;" placeholder="Enter reason for removing this officer..."></textarea>
            </div>

            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
                <button type="button" onclick="hideRemoveOfficerModal()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="btn btn-danger">
                    🗑️ Remove Officer
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function showRemoveOfficerModal(officerId, officerName, officerRole) {
    document.getElementById('officerName').textContent = officerName;
    document.getElementById('officerRole').textContent = officerRole.charAt(0).toUpperCase() + officerRole.slice(1);
    document.getElementById('removeOfficerForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/officers/') ?>' + officerId + '/remove';
    document.getElementById('removeOfficerModal').style.display = 'flex';
}

function hideRemoveOfficerModal() {
    document.getElementById('removeOfficerModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('removeOfficerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideRemoveOfficerModal();
    }
});
</script>

<?= $this->endSection() ?>
