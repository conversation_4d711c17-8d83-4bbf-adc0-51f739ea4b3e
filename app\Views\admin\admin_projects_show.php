<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects') ?>" class="btn btn-secondary">
    ← Back to Projects
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" class="btn btn-primary">
    ✏️ Edit Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Profile
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Complete information and phase management for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Overview Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 48px; height: 48px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 1.125rem;">
                📁
            </div>
            <div>
                <h2 style="color: var(--text-primary); font-size: 1.25rem; font-weight: 600; margin: 0;">
                    <?= esc($project['title']) ?>
                </h2>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
            <div style="margin-left: auto;">
                <?php
                $statusColors = [
                    'planning' => 'var(--text-muted)',
                    'active' => 'var(--brand-secondary)',
                    'on-hold' => 'var(--brand-warning)',
                    'completed' => 'var(--brand-primary)',
                    'cancelled' => 'var(--brand-danger)'
                ];
                $statusColor = $statusColors[$project['status']] ?? 'var(--text-muted)';
                ?>
                <span style="background: <?= $statusColor ?>; color: white; padding: var(--spacing-sm) var(--spacing-md); border-radius: var(--radius-md); font-size: 0.875rem; font-weight: 600; text-transform: uppercase;">
                    <?= esc($project['status']) ?>
                </span>
            </div>
        </div>
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <!-- Basic Information Grid -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
            
            <!-- Left Column -->
            <div>
                <!-- Project Goal -->
                <?php if ($project['goal']): ?>
                    <div style="margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                            Project Goal
                        </h4>
                        <p style="color: var(--text-secondary); line-height: 1.6; margin: 0;">
                            <?= esc($project['goal']) ?>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Other Project IDs -->
                <?php if ($project['other_project_ids']): ?>
                    <div style="margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                            External References
                        </h4>
                        <p style="color: var(--text-secondary); font-family: monospace; font-size: 0.875rem; margin: 0;">
                            <?= esc($project['other_project_ids']) ?>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Timeline Information -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                        Timeline
                    </h4>
                    <div style="display: grid; grid-template-columns: auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem;">
                        <?php if ($project['initiation_date']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Initiated:</span>
                            <span style="color: var(--text-secondary);"><?= date('F j, Y', strtotime($project['initiation_date'])) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['start_date']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Started:</span>
                            <span style="color: var(--text-secondary);"><?= date('F j, Y', strtotime($project['start_date'])) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['end_date']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">End Date:</span>
                            <span style="color: var(--text-secondary);"><?= date('F j, Y', strtotime($project['end_date'])) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['baseline_year']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Baseline Year:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['baseline_year']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['target_year']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Target Year:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['target_year']) ?></span>
                        <?php endif; ?>
                        
                        <?php if (!$project['initiation_date'] && !$project['start_date'] && !$project['end_date'] && !$project['baseline_year'] && !$project['target_year']): ?>
                            <span style="color: var(--text-muted); font-style: italic;" colspan="2">No timeline information available</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div>
                <!-- Location Information -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                        Location
                    </h4>
                    <div style="display: grid; grid-template-columns: auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem;">
                        <?php if ($project['address_line']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Address:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['address_line']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['country_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Country:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['country_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['province_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Province:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['province_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['district_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">District:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['district_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['llg_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">LLG:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['llg_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['ward_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Ward:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['ward_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['village_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Village:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['village_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['gps_point']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">GPS:</span>
                            <span style="color: var(--text-secondary); font-family: monospace;"><?= esc($project['gps_point']) ?></span>
                        <?php endif; ?>
                        
                        <?php if (!$project['address_line'] && !$project['country_name'] && !$project['province_name'] && !$project['district_name'] && !$project['llg_name'] && !$project['ward_name'] && !$project['village_name'] && !$project['gps_point']): ?>
                            <span style="color: var(--text-muted); font-style: italic;" colspan="2">No location information available</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Project Metadata -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                        Project Metadata
                    </h4>
                    <div style="display: grid; grid-template-columns: auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem;">
                        <span style="color: var(--text-muted); font-weight: 500;">Created:</span>
                        <span style="color: var(--text-secondary);"><?= date('F j, Y \a\t g:i A', strtotime($project['created_at'])) ?></span>
                        
                        <?php if ($project['creator_name']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Created by:</span>
                            <span style="color: var(--text-secondary);"><?= esc($project['creator_name']) ?></span>
                        <?php endif; ?>
                        
                        <?php if ($project['updated_at'] && $project['updated_at'] !== $project['created_at']): ?>
                            <span style="color: var(--text-muted); font-weight: 500;">Last Updated:</span>
                            <span style="color: var(--text-secondary);"><?= date('F j, Y \a\t g:i A', strtotime($project['updated_at'])) ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Description -->
        <?php if ($project['description']): ?>
            <div style="margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                    Project Description
                </h4>
                <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); color: var(--text-secondary); line-height: 1.6;">
                    <?= nl2br(esc($project['description'])) ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Status Notes -->
        <?php if ($project['status_notes']): ?>
            <div style="margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                    Status Notes
                </h4>
                <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); color: var(--text-secondary); line-height: 1.6;">
                    <?= nl2br(esc($project['status_notes'])) ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Phases -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>📋</span>
                <span>Project Phases</span>
            </div>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="btn btn-primary" style="margin-left: auto;">
                ➕ Add New Phase
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($phases)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php foreach ($phases as $phase): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); border-left: 4px solid <?= $phase['status'] === 'active' ? 'var(--brand-primary)' : 'var(--text-muted)' ?>;">
                        <div style="display: flex; align-items: start; justify-content: between; gap: var(--spacing-md);">
                            <!-- Phase Info -->
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                                    <h4 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                                        <?= esc($phase['title']) ?>
                                    </h4>
                                    <span style="background: <?= $phase['status'] === 'active' ? 'var(--brand-primary)' : 'var(--text-muted)' ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                        <?= esc($phase['status']) ?>
                                    </span>
                                    <span style="background: var(--bg-secondary); color: var(--text-muted); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 500;">
                                        Order: <?= esc($phase['sort_order']) ?>
                                    </span>
                                </div>

                                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                                    <span style="color: var(--text-muted); font-weight: 500;">Code:</span>
                                    <span style="color: var(--text-secondary); font-family: monospace;"><?= esc($phase['phase_code']) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Milestones:</span>
                                    <span style="color: var(--text-secondary);"><?= esc($phase['milestone_count'] ?? 0) ?></span>

                                    <?php if ($phase['start_date']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">Start:</span>
                                        <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($phase['start_date'])) ?></span>
                                    <?php endif; ?>

                                    <?php if ($phase['end_date']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">End:</span>
                                        <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($phase['end_date'])) ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($phase['description']): ?>
                                    <p style="color: var(--text-secondary); margin: 0; line-height: 1.5; font-size: 0.875rem;">
                                        <?= esc($phase['description']) ?>
                                    </p>
                                <?php endif; ?>
                            </div>

                            <!-- Phase Actions -->
                            <div style="display: flex; gap: var(--spacing-sm);">
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/' . $phase['id'] . '/edit') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    ✏️ Edit
                                </a>
                                <button onclick="showDeletePhaseModal(<?= $phase['id'] ?>, '<?= esc($phase['title']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📋</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Phases Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start organizing your project by creating the first phase.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="btn btn-primary">
                    ➕ Create First Phase
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Milestones -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>🎯</span>
                <span>Project Milestones</span>
            </div>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>" class="btn btn-primary" style="margin-left: auto;">
                ➕ Add New Milestone
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($milestones)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php
                $currentPhase = null;
                foreach ($milestones as $milestone):
                    // Group milestones by phase
                    if ($currentPhase !== $milestone['phase_title']):
                        if ($currentPhase !== null): ?>
                            </div> <!-- Close previous phase group -->
                        <?php endif; ?>
                        <div style="margin-bottom: var(--spacing-lg);">
                            <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin-bottom: var(--spacing-md); padding-bottom: var(--spacing-xs); border-bottom: 1px solid var(--border-color);">
                                📋 <?= esc($milestone['phase_title']) ?> (<?= esc($milestone['phase_code']) ?>)
                            </h4>
                            <div style="display: grid; gap: var(--spacing-md);">
                        <?php
                        $currentPhase = $milestone['phase_title'];
                    endif;
                ?>

                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-md); border-left: 4px solid <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'var(--brand-success)' : ($milestone['status'] === 'in-progress' ? 'var(--brand-warning)' : 'var(--brand-secondary)') ?>;">
                        <div style="display: flex; align-items: start; justify-content: between; gap: var(--spacing-md);">
                            <!-- Milestone Info -->
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                    <h5 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin: 0;">
                                        <?= esc($milestone['title']) ?>
                                    </h5>
                                    <span style="background: <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'var(--brand-success)' : ($milestone['status'] === 'in-progress' ? 'var(--brand-warning)' : 'var(--brand-secondary)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.625rem; font-weight: 600; text-transform: uppercase;">
                                        <?= esc($milestone['status']) ?>
                                    </span>
                                </div>

                                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-xs) var(--spacing-md); font-size: 0.75rem; margin-bottom: var(--spacing-xs);">
                                    <span style="color: var(--text-muted); font-weight: 500;">Code:</span>
                                    <span style="color: var(--text-secondary); font-family: monospace;"><?= esc($milestone['milestone_code']) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Evidence:</span>
                                    <span style="color: var(--text-secondary);"><?= esc($milestone['evidence_count'] ?? 0) ?></span>

                                    <?php if ($milestone['target_date']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">Target:</span>
                                        <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($milestone['target_date'])) ?></span>
                                    <?php endif; ?>

                                    <?php if ($milestone['completion_date']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">Completed:</span>
                                        <span style="color: var(--brand-success);"><?= date('M j, Y', strtotime($milestone['completion_date'])) ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($milestone['description']): ?>
                                    <p style="color: var(--text-secondary); margin: 0; line-height: 1.4; font-size: 0.75rem;">
                                        <?= esc($milestone['description']) ?>
                                    </p>
                                <?php endif; ?>
                            </div>

                            <!-- Milestone Actions -->
                            <div style="display: flex; gap: var(--spacing-xs);">
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/edit') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs); font-size: 0.625rem;">
                                    ✏️ Edit
                                </a>
                                <button onclick="showDeleteMilestoneModal(<?= $milestone['id'] ?>, '<?= esc($milestone['title']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs); font-size: 0.625rem;">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    </div>

                <?php endforeach; ?>
                <?php if ($currentPhase !== null): ?>
                    </div> <!-- Close last phase group -->
                </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🎯</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Milestones Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start tracking progress by creating project milestones.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>" class="btn btn-primary">
                    ➕ Create First Milestone
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Files & Documentation -->
<?php if ($project['gps_kml_path'] || $project['evaluation_file']): ?>
<div class="card mb-xl">
    <div class="card-header">
        📎 Files & Documentation
    </div>

    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl);">

            <!-- GPS KML File -->
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                    GPS KML File
                </h4>
                <?php if ($project['gps_kml_path']): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; gap: var(--spacing-md);">
                        <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            📍
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                <?= basename($project['gps_kml_path']) ?>
                            </div>
                            <div style="font-size: 0.75rem; color: var(--text-muted);">
                                KML/KMZ Geographic Data
                            </div>
                        </div>
                        <a href="<?= base_url($project['gps_kml_path']) ?>" target="_blank" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                            📥 Download
                        </a>
                    </div>
                <?php else: ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); text-align: center; color: var(--text-muted); font-style: italic;">
                        No GPS KML file uploaded
                    </div>
                <?php endif; ?>
            </div>

            <!-- Evaluation File -->
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm); text-transform: uppercase; letter-spacing: 0.5px;">
                    Evaluation Document
                </h4>
                <?php if ($project['evaluation_file']): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; gap: var(--spacing-md);">
                        <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            📄
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                <?= basename($project['evaluation_file']) ?>
                            </div>
                            <div style="font-size: 0.75rem; color: var(--text-muted);">
                                Project Evaluation Document
                            </div>
                        </div>
                        <a href="<?= base_url($project['evaluation_file']) ?>" target="_blank" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                            📥 Download
                        </a>
                    </div>
                <?php else: ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); text-align: center; color: var(--text-muted); font-style: italic;">
                        No evaluation document uploaded
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Project Budget -->
<div class="card mb-xl">
    <div class="card-header">
        💰 Project Budget
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Add Item
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <!-- Budget Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    $<?= number_format($budgetStats['total_planned'], 2) ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Budget</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= count($budgetItems) ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Active Items</div>
            </div>
        </div>

        <?php if (!empty($budgetItems)): ?>
            <!-- Recent Budget Items -->
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                $displayItems = array_slice($budgetItems, 0, 5); // Show only first 5 items
                foreach ($displayItems as $item): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin: 0;">
                                    <?= esc($item['item_code']) ?>
                                </h4>
                                <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                    $<?= number_format($item['amount_planned'], 2) ?>
                                </span>
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                                <?= esc($item['description']) ?>
                            </p>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/' . $item['id'] . '/edit') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($budgetItems) > 5): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary">
                            View All <?= count($budgetItems) ?> Budget Items
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">💰</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Budget Items Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start planning your project budget by adding budget items.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary">
                    💰 Add First Budget Item
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Outcomes -->
<div class="card mb-xl">
    <div class="card-header">
        🎯 Project Outcomes
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Add Outcome
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <!-- Outcomes Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $outcomeStats['total_outcomes'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Outcomes</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= number_format($outcomeStats['total_quantity'], 2) ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Quantity</div>
            </div>
        </div>

        <?php if (!empty($outcomes)): ?>
            <!-- Recent Outcomes -->
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                $displayOutcomes = array_slice($outcomes, 0, 5); // Show only first 5 outcomes
                foreach ($displayOutcomes as $outcome): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin: 0;">
                                    <?= number_format($outcome['quantity'], 2) ?> <?= $outcome['unit'] ? esc($outcome['unit']) : '' ?>
                                </h4>
                                <?php if ($outcome['unit']): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        <?= esc($outcome['unit']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                                <?= esc($outcome['outcome_text']) ?>
                            </p>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/' . $outcome['id'] . '/edit') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($outcomes) > 5): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary">
                            View All <?= count($outcomes) ?> Project Outcomes
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🎯</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Project Outcomes Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Define measurable deliverables to track project success metrics.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary">
                    🎯 Add First Outcome
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Issues Addressed -->
<div class="card mb-xl">
    <div class="card-header">
        🎯 Issues Addressed
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Add Issue
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <!-- Issues Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $issueStats['total_issues'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Issues</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= $issueStats['by_type']['direct'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Direct</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
                    <?= $issueStats['by_type']['indirect'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Indirect</div>
            </div>
        </div>

        <?php if (!empty($issues)): ?>
            <!-- Recent Issues -->
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                $displayIssues = array_slice($issues, 0, 5); // Show only first 5 issues
                foreach ($displayIssues as $issue): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                <?php if ($issue['issue_type'] === 'direct'): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        🎯 Direct
                                    </span>
                                <?php else: ?>
                                    <span style="background: var(--brand-accent); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        🔄 Indirect
                                    </span>
                                <?php endif; ?>
                            </div>
                            <p style="color: var(--text-primary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                                <?= esc($issue['description']) ?>
                            </p>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/' . $issue['id'] . '/edit') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($issues) > 5): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary">
                            View All <?= count($issues) ?> Issues Addressed
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🎯</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Issues Addressed Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Document the problems and challenges this project addresses for impact assessment.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary">
                    🎯 Add First Issue
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Impact Indicators -->
<div class="card mb-xl">
    <div class="card-header">
        📊 Impact Indicators
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📈 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Add Indicator
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <!-- Indicators Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $indicatorSummary['total_indicators'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Indicators</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $indicatorSummary['indicators_with_actual'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">With Actual Values</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
                    <?= $indicatorSummary['targets_achieved'] + $indicatorSummary['targets_exceeded'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Targets Met</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= number_format($indicatorSummary['average_achievement_rate'], 1) ?>%
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Avg Achievement</div>
            </div>
        </div>

        <?php if (!empty($indicators)): ?>
            <!-- Recent Indicators -->
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                $displayIndicators = array_slice($indicators, 0, 5); // Show only first 5 indicators
                foreach ($displayIndicators as $indicator): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin: 0;">
                                    <?= esc($indicator['indicator_text']) ?>
                                </h4>
                                <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '' &&
                                          $indicator['target_value'] !== null && $indicator['target_value'] !== '' &&
                                          is_numeric($indicator['actual_value']) && is_numeric($indicator['target_value']) &&
                                          $indicator['target_value'] > 0): ?>
                                    <?php
                                    $achievementRate = ((float)$indicator['actual_value'] / (float)$indicator['target_value']) * 100;
                                    $statusColor = $achievementRate >= 100 ? 'var(--brand-success)' : ($achievementRate >= 75 ? 'var(--brand-warning)' : 'var(--brand-danger)');
                                    ?>
                                    <span style="background: <?= $statusColor ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        <?= number_format($achievementRate, 1) ?>%
                                    </span>
                                <?php elseif ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '' &&
                                              (!is_numeric($indicator['actual_value']) || !is_numeric($indicator['target_value']))): ?>
                                    <span style="background: var(--brand-info); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        COMPLETED
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div style="display: grid; grid-template-columns: auto 1fr auto 1fr auto 1fr; gap: var(--spacing-xs) var(--spacing-md); font-size: 0.75rem;">
                                <?php if ($indicator['baseline_value'] !== null && $indicator['baseline_value'] !== ''): ?>
                                    <span style="color: var(--text-muted); font-weight: 500;">Baseline:</span>
                                    <span style="color: var(--text-secondary);"><?= is_numeric($indicator['baseline_value']) ? number_format($indicator['baseline_value'], 2) : esc($indicator['baseline_value']) ?></span>
                                <?php endif; ?>

                                <?php if ($indicator['target_value'] !== null && $indicator['target_value'] !== ''): ?>
                                    <span style="color: var(--text-muted); font-weight: 500;">Target:</span>
                                    <span style="color: var(--brand-accent);"><?= is_numeric($indicator['target_value']) ? number_format($indicator['target_value'], 2) : esc($indicator['target_value']) ?></span>
                                <?php endif; ?>

                                <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== ''): ?>
                                    <span style="color: var(--text-muted); font-weight: 500;">Actual:</span>
                                    <span style="color: var(--brand-success);"><?= is_numeric($indicator['actual_value']) ? number_format($indicator['actual_value'], 2) : esc($indicator['actual_value']) ?></span>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-weight: 500;">Actual:</span>
                                    <span style="color: var(--text-muted); font-style: italic;">Pending</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/' . $indicator['id'] . '/edit') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($indicators) > 5): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary">
                            View All <?= count($indicators) ?> Impact Indicators
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📊</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Impact Indicators Yet</h3>
                <p style="margin-bottom: var(--spacing-lg);">Define measurable impact metrics for monitoring and evaluation framework.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary">
                    📊 Add First Indicator
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Risks -->
<div class="card mb-xl">
    <div class="card-header">
        ⚠️ Project Risks
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Add Risk
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($risks)): ?>
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                // Show only first 3 risks in project profile
                $displayRisks = array_slice($risks, 0, 3);
                foreach ($displayRisks as $risk):
                    // Calculate risk score
                    $riskScores = ['low' => 1, 'medium' => 2, 'high' => 3, 'critical' => 4];
                    $riskScore = $riskScores[$risk['risk_level']] ?? 1;
                ?>
                    <div style="padding: var(--spacing-lg); border: 1px solid var(--border-light); border-radius: var(--radius-md); background: var(--bg-secondary);">
                        <div style="display: flex; justify-content: between; align-items: start; gap: var(--spacing-md);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc(substr($risk['description'], 0, 100)) ?><?= strlen($risk['description']) > 100 ? '...' : '' ?>
                                </div>
                                <div style="display: flex; gap: var(--spacing-md); font-size: 0.75rem;">
                                    <span class="badge badge-<?= $risk['risk_type'] === 'witnessed' ? 'danger' : ($risk['risk_type'] === 'foreseen' ? 'warning' : 'info') ?>">
                                        <?= ucfirst($risk['risk_type']) ?>
                                    </span>
                                    <span class="badge badge-<?= $risk['risk_level'] === 'critical' ? 'danger' : ($risk['risk_level'] === 'high' ? 'warning' : ($risk['risk_level'] === 'medium' ? 'info' : 'secondary')) ?>">
                                        <?= ucfirst($risk['risk_level']) ?> Level
                                    </span>
                                    <span class="badge badge-<?= $riskScore >= 4 ? 'danger' : ($riskScore >= 3 ? 'warning' : ($riskScore >= 2 ? 'info' : 'secondary')) ?>">
                                        Score: <?= $riskScore ?>/4
                                    </span>
                                </div>
                            </div>
                            <div style="display: flex; gap: var(--spacing-xs);">
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/' . $risk['id'] . '/edit') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs); font-size: 0.7rem;">
                                    ✏️
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($risks) > 3): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-outline">
                            View All <?= count($risks) ?> Risks
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">⚠️</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Risks Identified</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start managing project risks by identifying potential issues.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary">
                    ⚠️ Add First Risk
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Officers -->
<div class="card mb-xl">
    <div class="card-header">
        👥 Project Officers
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                ➕ Assign Officer
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <!-- Officers Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $officerStats['total_officers'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Officers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $officerStats['by_role']['lead'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Lead Officers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
                    <?= $officerStats['by_role']['certifier'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Certifiers</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
                    <?= $officerStats['by_role']['support'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Support Officers</div>
            </div>
        </div>

        <?php if (!empty($officers)): ?>
            <!-- Recent Officers -->
            <div style="display: grid; gap: var(--spacing-md);">
                <?php
                $displayOfficers = array_slice($officers, 0, 5); // Show only first 5 officers
                foreach ($displayOfficers as $officer): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin: 0;">
                                    <?= esc($officer['name']) ?>
                                </h4>
                                <span style="background: <?= $officer['role'] === 'lead' ? 'var(--brand-success)' : ($officer['role'] === 'certifier' ? 'var(--brand-secondary)' : 'var(--brand-accent)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?php if ($officer['role'] === 'lead'): ?>
                                        👑 Lead
                                    <?php elseif ($officer['role'] === 'certifier'): ?>
                                        ✅ Certifier
                                    <?php else: ?>
                                        🤝 Support
                                    <?php endif; ?>
                                </span>
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                                <?= esc($officer['username']) ?> • <?= esc($officer['email']) ?>
                            </p>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                👥 Manage
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($officers) > 5): ?>
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-secondary">
                            View All <?= count($officers) ?> Project Officers
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👥</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Officers Assigned</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start building your project team by assigning officers with specific roles.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary">
                    ➕ Assign First Officer
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Documents -->
<a id="project-documents"></a>
<div class="card mb-xl">
    <div class="card-header">
        📁 Project Documents
        <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📊 View All
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                📁 Upload Document
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php
        // Get recent documents for display (limit to 5)
        $documentModel = new \App\Models\ProjectDocumentModel();
        $recentDocuments = $documentModel->getByProject($project['id']);
        $recentDocuments = array_slice($recentDocuments, 0, 5);
        $documentStats = $documentModel->getDocumentStatistics($project['id']);
        ?>

        <?php if (!empty($recentDocuments)): ?>
            <!-- Document Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                <div style="text-align: center; padding: var(--spacing-md); background: var(--bg-secondary); border-radius: var(--radius-md);">
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--brand-primary);"><?= $documentStats['total_documents'] ?? 0 ?></div>
                    <div style="font-size: 0.75rem; color: var(--text-muted); text-transform: uppercase; letter-spacing: 0.5px;">Total Documents</div>
                </div>

                <div style="text-align: center; padding: var(--spacing-md); background: var(--bg-secondary); border-radius: var(--radius-md);">
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--brand-info);"><?= $documentStats['recent_uploads'] ?? 0 ?></div>
                    <div style="font-size: 0.75rem; color: var(--text-muted); text-transform: uppercase; letter-spacing: 0.5px;">Recent Uploads</div>
                </div>
            </div>

            <!-- Recent Documents List -->
            <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                <?php foreach ($recentDocuments as $document): ?>
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--spacing-md); background: var(--bg-secondary); border-radius: var(--radius-md);">
                        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                            <div style="width: 32px; height: 32px; border-radius: var(--radius-sm); background: var(--brand-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem;">
                                📄
                            </div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: var(--spacing-xs);">
                                    <?= !empty($document['description']) ? esc($document['description']) : 'Document' ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= esc(basename($document['doc_path'])) ?> • v<?= $document['version_no'] ?? 1 ?> •
                                    <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>"
                               style="padding: var(--spacing-xs) var(--spacing-sm); background: var(--brand-primary); color: white; border-radius: var(--radius-sm); text-decoration: none; font-size: 0.75rem;">
                                📥 Download
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📁</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Documents Uploaded</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start organizing project documents by uploading your first file.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
                    📁 Upload First Document
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        ⚡ Quick Actions
    </div>

    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">

            <!-- Edit Project -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        ✏️
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Edit Project
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Update project information
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Budget -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-accent); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        💰
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Budget
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Budget planning & tracking
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Outcomes -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-warning); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        🎯
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Outcomes
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Define success metrics
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Issues -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-info); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        🎯
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Issues
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Document impact assessment
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Impact Indicators -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        📊
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Impact Indicators
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            M&E measurement framework
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Project Risks -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-danger); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        ⚠️
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Risks
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Risk identification & management
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Project Officers -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-accent); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        👥
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Officers
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Assign & manage project team
                        </div>
                    </div>
                </div>
            </a>

            <!-- Manage Project Documents -->
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-info); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        📁
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Manage Documents
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Upload & organize project files
                        </div>
                    </div>
                </div>
            </a>

            <!-- Back to Projects -->
            <a href="<?= base_url('admin/projects') ?>" class="action-card">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        📋
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            All Projects
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Return to project list
                        </div>
                    </div>
                </div>
            </a>

            <!-- Delete Project -->
            <button onclick="showDeleteModal()" class="action-card" style="border: none; background: none; cursor: pointer; width: 100%;">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-lg); background: var(--bg-tertiary); border-radius: var(--radius-md); text-decoration: none; color: inherit; transition: all 0.2s ease;">
                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--brand-danger); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                        🗑️
                    </div>
                    <div style="text-align: left;">
                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                            Delete Project
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                            Permanently remove project
                        </div>
                    </div>
                </div>
            </button>
        </div>
    </div>
</div>

<!-- Delete Milestone Confirmation Modal -->
<div id="deleteMilestoneModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Milestone</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the milestone <strong id="milestoneNameToDelete"></strong>?
        </p>
        <p style="color: var(--brand-danger); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            This action cannot be undone. All milestone data will be permanently removed.
        </p>

        <form id="deleteMilestoneForm" method="post" action="" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteMilestoneModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDeleteMilestone()" class="btn btn-danger">Delete Milestone</button>
        </div>
    </div>
</div>

<!-- Delete Phase Confirmation Modal -->
<div id="deletePhaseModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Phase</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the phase <strong id="phaseNameToDelete"></strong>?
        </p>
        <p style="color: var(--brand-danger); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            This action cannot be undone. All phase data will be permanently removed.
        </p>

        <form id="deletePhaseForm" method="post" action="" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeletePhaseModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDeletePhase()" class="btn btn-danger">Delete Phase</button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Project</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the project <strong><?= esc($project['title']) ?></strong>?
        </p>
        <p style="color: var(--brand-danger); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            This action cannot be undone. All project data will be permanently removed.
        </p>

        <form id="deleteForm" method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/delete') ?>" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Project</button>
        </div>
    </div>
</div>

<style>
.action-card:hover > div {
    background: var(--bg-accent) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }

    div[style*="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr))"] {
        grid-template-columns: 1fr !important;
    }
}
</style>

<script>
// Milestone delete modal functions
function showDeleteMilestoneModal(milestoneId, milestoneName) {
    document.getElementById('milestoneNameToDelete').textContent = milestoneName;
    document.getElementById('deleteMilestoneForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/milestones/') ?>' + milestoneId + '/delete';
    document.getElementById('deleteMilestoneModal').style.display = 'flex';
}

function hideDeleteMilestoneModal() {
    document.getElementById('deleteMilestoneModal').style.display = 'none';
}

function confirmDeleteMilestone() {
    document.getElementById('deleteMilestoneForm').submit();
}

// Phase delete modal functions
function showDeletePhaseModal(phaseId, phaseName) {
    document.getElementById('phaseNameToDelete').textContent = phaseName;
    document.getElementById('deletePhaseForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/phases/') ?>' + phaseId + '/delete';
    document.getElementById('deletePhaseModal').style.display = 'flex';
}

function hideDeletePhaseModal() {
    document.getElementById('deletePhaseModal').style.display = 'none';
}

function confirmDeletePhase() {
    document.getElementById('deletePhaseForm').submit();
}

// Project delete modal functions
function showDeleteModal() {
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

function confirmDelete() {
    document.getElementById('deleteForm').submit();
}

// Close modals when clicking outside
document.getElementById('deleteMilestoneModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteMilestoneModal();
    }
});

document.getElementById('deletePhaseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeletePhaseModal();
    }
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
